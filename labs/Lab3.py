# Lab 3 
# CPAN 214
# Student: <PERSON><PERSON><PERSON><PERSON><PERSON>

def main():
    nums = []

    while True:
        user_input = input("Enter a number or a list of numbers separated by spaces: ")

        try:
            # Split input and convert each item to float
            new_nums = [float(num) for num in user_input.split()]
            nums.extend(new_nums)
        except ValueError:
            print("Invalid input. Please enter only numbers.")
            continue

        another = input("Would you like to enter another number? (no/yes): ").strip().lower()
        if another != 'yes':
            break

    if nums:
        max_num = max(nums)
        min_num = min(nums)
        print("\nNumbers Entered:", nums)
        print("Maximum Number:", max_num)
        print("Minimum Number:", min_num)
    else:
        print("No numbers were entered.")

if __name__ == "__main__":
    main()
