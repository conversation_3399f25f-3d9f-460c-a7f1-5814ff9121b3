# Step 9 & 10: Use while loop to repeat program unless user exits
while True:

    # Step 1: Ask user for number of elements
    num_elements = int(input("Enter the number of elements: "))

    # Step 7: Initialize counters
    num_fizz = 0
    num_buzz = 0
    num_fizz_buzz = 0

    # Step 2: For loop from 1 to num_elements (inclusive)
    for i in range(1, num_elements + 1):

        # Step 3: Print iteration number
        print(f"Iteration number: {i}")

        # Step 4, 5, 6: Check divisibility and print Fizz, Buzz or FizzBuzz
        if i % 3 == 0 and i % 5 == 0:
            print("FizzBuzz")
            num_fizz_buzz += 1
        elif i % 3 == 0:
            print("Fizz")
            num_fizz += 1
        elif i % 5 == 0:
            print("Buzz")
            num_buzz += 1

    # Step 8: Print summary of occurrences
    print(f"\nSummary for {num_elements} iterations:")
    print(f"Fizz occurred: {num_fizz} times")
    print(f"Buzz occurred: {num_buzz} times")
    print(f"FizzBuzz occurred: {num_fizz_buzz} times\n")

    # Step 9: Ask to exit
    exit_input = input("Would you like to exit? (y/n): ").strip().lower()
    if exit_input == "y":
        print("Exiting the program. Bye Bye! Have a good one!")
        break