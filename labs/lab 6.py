import os

# Create data folder if it doesn't exist
os.makedirs("data", exist_ok=True)

# 1 & 2. Basketball team sets
basketball_team_5 = {"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"}
basketball_team_9 = {"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"}

# 3. Soccer & Baseball teams
baseball_team = {"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"}
soccer_team = {"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"}

try:
    # 4–5. Write teams to files
    with open("data/basketball_set.txt", "w") as f:
        f.write("\n".join(basketball_team_9))
    print("🏀 Basketball Team:", basketball_team_9, flush=True)

    with open("data/baseball_set.txt", "w") as f:
        f.write("\n".join(baseball_team))
    print("⚾ Baseball Team:", baseball_team, flush=True)

    with open("data/soccer_set.txt", "w") as f:
        f.write("\n".join(soccer_team))
    print("⚽ Soccer Team:", soccer_team, flush=True)

    # 6. Intersections
    intersection_bs = basketball_team_9.intersection(soccer_team)
    with open("data/intersection_basketball_soccer.txt", "w") as f:
        f.write("\n".join(intersection_bs))
    print("📌 Intersection (Basketball ∩ Soccer):", intersection_bs, flush=True)

    intersection_bbs = baseball_team.intersection(soccer_team)
    with open("data/intersection_baseball_soccer.txt", "w") as f:
        f.write("\n".join(intersection_bbs))
    print("📌 Intersection (Baseball ∩ Soccer):", intersection_bbs, flush=True)

    # 7. Union
    union_bb = basketball_team_9.union(baseball_team)
    with open("data/union_basketball_baseball.txt", "w") as f:
        f.write("\n".join(union_bb))
    print("📌 Union (Basketball ∪ Baseball):", union_bb, flush=True)

    # 8. Differences
    diff_b_s = basketball_team_9.difference(soccer_team)
    with open("data/difference_basketball_soccer.txt", "w") as f:
        f.write("\n".join(diff_b_s))
    print("📌 Difference (Basketball - Soccer):", diff_b_s, flush=True)

    diff_b_b = baseball_team.difference(basketball_team_9)
    with open("data/difference_baseball_basketball.txt", "w") as f:
        f.write("\n".join(diff_b_b))
    print("📌 Difference (Baseball - Basketball):", diff_b_b, flush=True)

    # 9. Nested tuple
    nested_tuple = (1, 2, (3, 4, (6, 7)))
    with open("data/nested_tuple.txt", "w") as f:
        f.write(str(nested_tuple))
    print("🧩 Nested Tuple:", nested_tuple, flush=True)

    # 10–11. Flatten and write
    def flatten(t):
        for i in t:
            if isinstance(i, tuple):
                yield from flatten(i)
            else:
                yield i

    flattened = tuple(flatten(nested_tuple))
    with open("data/flattened_tuple.txt", "w") as f:
        f.write(str(flattened))
    print("🔄 Flattened Tuple:", flattened, flush=True)

    # 13–14. Even numbers
    start_even = int(input("Enter start of range for even numbers: "))
    end_even = int(input("Enter end of range: "))
    even_numbers = [x for x in range(start_even, end_even + 1) if x % 2 == 0]
    with open("data/even_numbers.txt", "w") as f:
        f.write(str(even_numbers))
    print("📥 Even Numbers:", even_numbers, flush=True)

    # 15–16. Square numbers
    start_sq = int(input("Enter start of range to square numbers: "))
    end_sq = int(input("Enter end of range: "))
    squared_numbers = [x ** 2 for x in range(start_sq, end_sq + 1)]
    with open("data/squared_numbers.txt", "w") as f:
        f.write(str(squared_numbers))
    print("📥 Squared Numbers:", squared_numbers, flush=True)

    # Bonus: Show contents of all files
    print("\n📂 Verifying contents of all files:", flush=True)
    for filename in os.listdir("data"):
        print(f"\n--- {filename} ---", flush=True)
        with open(os.path.join("data", filename), "r") as f:
            print(f.read(), flush=True)

except Exception as e:
    print("🚨 An error occurred:", e, flush=True)
