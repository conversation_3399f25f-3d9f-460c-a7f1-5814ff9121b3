# Task 1 & 2
x = 4
y = 2

# Task 3
print("x + y =", x + y)

# Task 4 & 5
car_name = "<PERSON>"
num_wheels = 4

# Task 6
print(f"The car is a {car_name} and it has {num_wheels} wheels.")

# Task 7, 8 & 9
name = "<PERSON><PERSON><PERSON><PERSON><PERSON>"
num_apples = 24
num_friends = 7
apples_per_friend = num_apples / num_friends

# Task 10
print(f"{name} has {num_apples} apples and {num_friends} friends. Each friend gets {apples_per_friend:.2f} apples.")

# Task 11
bill_total = 120
num_people = 6
cost_per_person = bill_total / num_people

# Task 12
print(f"Each of the {num_people} people pays ${cost_per_person:.2f} when the total bill is ${bill_total}.")

# Task 13
print(f"Each of the {num_people} people pays ${int(cost_per_person)} when the total bill is ${bill_total}.")

# Task 14
dessert_cost = 6.55
total_cost_of_desserts = dessert_cost * num_people

# Task 15
print(f"The dessert costs ${dessert_cost} per person. The total cost for desserts for {num_people} people is ${total_cost_of_desserts:.2f}.")