import random

# Function to get range from user and generate random number
def get_random_number():
    while True:
        try:
            min_num = int(input("Enter minimum number for range: "))
            max_num = int(input("Enter maximum number for range: "))
            if min_num >= max_num:
                print("Minimum must be less than maximum. Try again.")
            else:
                break
        except ValueError:
            print("Please enter valid integers.")
    return random.randint(min_num, max_num)

# Function to check divisibility by 3 and 5
def check_divisibility(number):
    if number % 3 == 0 and number % 5 == 0:
        return "FizzBuzz"
    elif number % 3 == 0:
        return "Fizz"
    elif number % 5 == 0:
        return "Buzz"
    else:
        return "Not divisible by 3 or 5"

# Function to print win stats and optionally reset
def display_statistics(wins, total_games):
    if total_games == 0:
        percent = 0
    else:
        percent = (wins / total_games) * 100
    print(f"\nGames won: {wins}")
    print(f"Total games played: {total_games}")
    print(f"Win percentage: {percent:.2f}%")

    choice = input("Would you like to reset statistics? (yes/no): ").strip().lower()
    if choice == "yes":
        return 0, 0
    return wins, total_games

# Main game loop
def main():
    wins = 0
    total_games = 0

    while True:
        secret_number = get_random_number()
        try:
            guess = int(input("Guess the number: "))
        except ValueError:
            print("Invalid input. Please enter a number.")
            continue

        print("Divisibility check:", check_divisibility(secret_number))

        if guess == secret_number:
            print("\n🎉 Correct! You guessed the number!")
            wins += 1
        else:
            print(f"\n❌ Incorrect. The correct number was {secret_number}.")

        total_games += 1

        # Show stats and ask to reset only once
        wins, total_games = display_statistics(wins, total_games)

        again = input("Would you like to play again? (yes/no): ").strip().lower()
        if again != "yes":
            print("Thanks for playing!")
            break

if __name__ == "__main__":
    main()
