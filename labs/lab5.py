def main():
    # Sample student data dictionary with nested dictionaries for courses
    students = {
        101: {
            "name": "<PERSON>",
            "age": 20,
            "school_name": "Humber College",
            "completed_courses": {
                "Math": 85,
                "English": 78
            },
            "ongoing_courses": {
                "Physics": 90,
                "Programming": 88
            }
        },
        102: {
            "name": "<PERSON>",
            "age": 22,
            "school_name": "Humber College",
            "completed_courses": {
                "Math": 75,
                "English": 82
            },
            "ongoing_courses": {
                "Physics": 85,
                "Programming": 80
            }
        }
    }

    def print_student_info(student_id):
        student = students.get(student_id)
        if student:
            print(f"ID: {student_id}")
            print(f"Name: {student['name']}")
            print(f"Age: {student['age']}")
            print(f"School: {student['school_name']}")
            print("Completed Courses:")
            for course, grade in student["completed_courses"].items():
                print(f"  {course}: {grade}")
            print("Ongoing Courses:")
            for course, grade in student["ongoing_courses"].items():
                print(f"  {course}: {grade}")
        else:
            print("Student ID not found.")

    def print_all_students():
        for student_id in students:
            print_student_info(student_id)
            print("-" * 20)

    def print_ongoing_grades(student_id):
        student = students.get(student_id)
        if student:
            print(f"Ongoing courses for {student['name']}:")
            for course, grade in student["ongoing_courses"].items():
                print(f"{course}: {grade}")
        else:
            print("Student ID not found.")

    def print_completed_grades(student_id):
        student = students.get(student_id)
        if student:
            print(f"Completed courses for {student['name']}:")
            for course, grade in student["completed_courses"].items():
                print(f"{course}: {grade}")
        else:
            print("Student ID not found.")

    def print_average_completed_grade(student_id):
        student = students.get(student_id)
        if student:
            grades = student["completed_courses"].values()
            if grades:
                average = sum(grades) / len(grades)
                print(f"Average completed grade for {student['name']}: {average:.2f}")
            else:
                print(f"No completed courses for {student['name']}.")
        else:
            print("Student ID not found.")

    def print_specific_grade(student_id, course_name):
        student = students.get(student_id)
        if not student:
            print("Student ID not found.")
            return

        # Check both completed and ongoing courses
        if course_name in student["completed_courses"]:
            grade = student["completed_courses"][course_name]
            print(f"{student['name']}'s completed grade in {course_name}: {grade}")
        elif course_name in student["ongoing_courses"]:
            grade = student["ongoing_courses"][course_name]
            print(f"{student['name']}'s ongoing grade in {course_name}: {grade}")
        else:
            print(f"Course '{course_name}' not found for student {student['name']}.")

    while True:
        print("\nSelect an option:")
        print("1. View all students’ information")
        print("2. View all information on specific student")
        print("3. View all ongoing grades of specific student")
        print("4. View all completed grades of specific student")
        print("5. View average completed grades of student")
        print("6. View specific grade of specific student")

        choice = input("Enter your choice (1-6): ").strip()

        if choice == "1":
            print_all_students()
        elif choice in {"2", "3", "4", "5", "6"}:
            try:
                student_id = int(input("Enter student ID: "))
            except ValueError:
                print("Invalid student ID. Must be a number.")
                continue

            if choice == "2":
                print_student_info(student_id)
            elif choice == "3":
                print_ongoing_grades(student_id)
            elif choice == "4":
                print_completed_grades(student_id)
            elif choice == "5":
                print_average_completed_grade(student_id)
            elif choice == "6":
                course_name = input("Enter course name: ").strip()
                print_specific_grade(student_id, course_name)
        else:
            print("Invalid choice. Please select a number from 1 to 6.")

        cont = input("\nWould you like to perform another query? (y/n): ").strip().lower()
        if cont != "y":
            print("Exiting program. Goodbye!")
            break


if __name__ == "__main__":
    main()
